#3      _DropdownButtonState._handleTap.<anonymous closure> (package:flutter/src/material/dropdown.dart:1446:25)
#4      Future._propagateToListeners.handleValueCallback (dart:async/future_impl.dart:951:45)
#5      Future._propagateToListeners (dart:async/future_impl.dart:980:13)
#6      Future._completeWithValue (dart:async/future_impl.dart:723:5)
#7      Future._asyncCompleteWithValue.<anonymous closure> (dart:async/future_impl.dart:807:7)
#8      _microtaskLoop (dart:async/schedule_microtask.dart:40:21)
#9      _startMicrotaskLoop (dart:async/schedule_microtask.dart:49:5)

NaN
NaN
❌ خطأ Flutter: The Scrollbar's ScrollController has no ScrollPosition attached.
A Scrollbar cannot be painted without a ScrollPosition.
The Scrollbar attempted to use the provided ScrollController. This ScrollController should be associated with the ScrollView that the Scrollbar is being applied to.
When providing your own ScrollController, ensure both the Scrollbar and the Scrollable widget use the same one.
📍 المكان: during a scheduler callback
Another exception was thrown: The Scrollbar's ScrollController has no ScrollPosition
attached.
NaN
NaN
NaN
NaN
[ERROR:flutter/runtime/dart_vm_initializer.cc(40)] Unhandled Exception: setState() callback argument returned a Future.
The setState() method on _TopBarState#4ef05 was called with a closure or method that returned a Future. Maybe it is marked as "async".
Instead of performing asynchronous work inside a call to setState(), first execute the work (without updating the widget state), and then synchronously update the state inside a call to setState().
#0      State.setState.<anonymous closure> (package:flutter/src/widgets/framework.dart:1202:9)
#1      State.setState (package:flutter/src/widgets/framework.dart:1218:6)
#2      _TopBarState.build.<anonymous closure>.<anonymous closure> (package:amrdev_win_pos/Screen/Widgets/TopBar/top_bar_widget.dart:360:23)
#3      _DropdownButtonState._handleTap.<anonymous closure> (package:flutter/src/material/dropdown.dart:1446:25)
#4      Future._propagateToListeners.handleValueCallback (dart:async/future_impl.dart:951:45)
#5      Future._propagateToListeners (dart:async/future_impl.dart:980:13)
#6      Future._completeWithValue (dart:async/future_impl.dart:723:5)
#7      Future._asyncCompleteWithValue.<anonymous closure> (dart:async/future_impl.dart:807:7)
#8      _microtaskLoop (dart:async/schedule_microtask.dart:40:21)
#9      _startMicrotaskLoop (dart:async/schedule_microtask.dart:49:5)

NaN
NaN
❌ خطأ Flutter: The Scrollbar's ScrollController has no ScrollPosition attached.
A Scrollbar cannot be painted without a ScrollPosition.
The Scrollbar attempted to use the provided ScrollController. This ScrollController should be associated with the ScrollView that the Scrollbar is being applied to.
When providing your own ScrollController, ensure both the Scrollbar and the Scrollable widget use the same one.
📍 المكان: during a scheduler callback
Another exception was thrown: The Scrollbar's ScrollController has no ScrollPosition
attached.
NaN
NaN
NaN
NaN
NaN
NaN

ℹ️ PayPal معطل للسوق المصري
NaN
NaN
Performing hot reload...                                                
Reloaded 157 of 2872 libraries in 6,094ms (compile: 4270 ms, reload: 672 ms, reassemble:  
802 ms).
❌ خطأ Flutter: A RenderFlex overflowed by 35 pixels on the bottom.
📍 المكان: during layout

══╡ EXCEPTION CAUGHT BY RENDERING LIBRARY
╞═════════════════════════════════════════════════════════
The following assertion was thrown during layout:
A RenderFlex overflowed by 35 pixels on the bottom.

The relevant error-causing widget was:
  Column Column:file:///D:/work/pos-win/lib/Screen/POS%20Sale/pos_sale.dart:2351:54       

To inspect this widget in Flutter DevTools, visit:
http://127.0.0.1:9105/#/inspector?uri=http%3A%2F%2F127.0.0.1%3A50742%2FJR6PYVufInE%3D%2F&i
nspectorRef=inspector-21

The overflowing RenderFlex has an orientation of Axis.vertical.
The edge of the RenderFlex that is overflowing has been marked in the rendering with a    
yellow and
black striped pattern. This is usually caused by the contents being too big for the       
RenderFlex.
Consider applying a flex factor (e.g. using an Expanded widget) to force the children of  
the
RenderFlex to fit within the available space instead of being sized to their natural size.
This is considered an error condition because it indicates that there is content that     
cannot be
seen. If the content is legitimately bigger than the available space, consider clipping it
with a
ClipRect widget before putting it in the flex, or using a scrollable container rather than
a Flex,
like a ListView.
The specific RenderFlex in question is: RenderFlex#0618b OVERFLOWING:
  creator: Column ← ColoredBox ← ConstrainedBox ← Container ← Expanded ← Row ← Column ←   
  Padding ←
    Column ← SizedBox ← _SingleChildViewport ← IgnorePointer-[GlobalKey#65724] ← ⋯        
  parentData: <none> (can use size)
  constraints: BoxConstraints(w=605.0, h=584.0)
  size: Size(605.0, 584.0)
  direction: vertical
  mainAxisAlignment: start
  mainAxisSize: min
  crossAxisAlignment: center
  verticalDirection: down
  spacing: 0.0
◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤
◢◤◢◤◢◤◢◤◢◤
══════════════════════════════════════════════════════════════════════════════════════════
══════════


🔍 فحص الاتصال...
Performing hot restart...                                               
Restarted application in 2,149ms.
🚀 بدء تحسين الأداء للتطبيق على Windows...
✅ تم تحسين إعدادات النظام
🧹 تنظيف الذاكرة...
✅ تم تحسين إعدادات الذاكرة
🌐 تحسين إعدادات الشبكة...
✅ تم تحسين إعدادات الشبكة
✅ تم تحسين الأداء بنجاح
🔧 تهيئة خدمة الاستقرار...
✅ تم تهيئة خدمة الاستقرار بنجاح
ℹ️ PayPal معطل للسوق المصري
🔍 بدء تشخيص مشاكل تسجيل الدخول...
📧 الإيميل: <EMAIL>
🔒 كلمة المرور: ******

🔐 فحص Firebase Authentication...
✅ تم تسجيل الدخول بنجاح في Firebase Auth
📧 الإيميل: <EMAIL>
🆔 UID: qLV4Q2IUOIdBs2ZgpSIkPJvJeWE2
✅ تم التحقق: false
🔑 تم الحصول على ID Token بنجاح

🗄️ فحص Firebase Database...
✅ تم كتابة البيانات بنجاح
✅ تم قراءة البيانات بنجاح
📊 البيانات: {"message":"اختبار الاتصال","platform":"Windows","timestamp":1752688723829}  
✅ تم حذف البيانات التجريبية

👥 فحص بيانات المستخدمين الفرعيين...
ℹ️ لا توجد بيانات مستخدمين فرعيين
💡 هذا طبيعي إذا كان المستخدم رئيسي
✅ انتهاء التشخيص
🔐 تم تسجيل الدخول بنجاح في Firebase Auth
👤 المستخدم: <EMAIL>
🔍 بدء التحقق من المستخدمين الفرعيين للإيميل: <EMAIL>
📊 تم الحصول على بيانات User Role من Firebase
❌ لا توجد بيانات في Admin Panel/User Role
🔄 انتهاء التحقق - النتيجة: false (لا توجد بيانات)
🔍 نتيجة فحص المستخدم الفرعي: false
👤 مستخدم رئيسي - فحص إعداد الملف الشخصي
🔍 فحص إعداد الملف الشخصي...
🆔 معرف المستخدم: qLV4Q2IUOIdBs2ZgpSIkPJvJeWE2
📊 نتيجة الاستعلام: exists=true, value={businessCategory: Paints, companyName: Tayeb, countryName: Qina, currency: $, currentLocale: ar, dueInvoiceCounter: 1, gst: 0, language: العربية, phoneNumber: ***********, pictureUrl: https://firebasestorage.googleapis.com/v0/b/maanpos.appspot.com/o/Profile%20Picture%2Fblank-profile-picture-973460_1280.webp?alt=media&token=3578c1e0-7278-4c03-8b56-dd007a9befd3, purchaseInvoiceCounter: 1, remainingShopBalance: 0, saleInvoiceCounter: 1, shopOpeningBalance: 0}
✅ تم العثور على معلومات شخصية - الملف الشخصي معد
🔍 حالة إعداد الملف الشخصي: true
✅ الملف الشخصي معد - الانتقال للشاشة الرئيسية
NaN
NaN
unhandled element <filter/>; Picture key: Svg loader
❌ خطأ Flutter: The Scrollbar's ScrollController has no ScrollPosition attached.
A Scrollbar cannot be painted without a ScrollPosition.
The Scrollbar attempted to use the provided ScrollController. This ScrollController should be associated with the ScrollView that the Scrollbar is being applied to.
When providing your own ScrollController, ensure both the Scrollbar and the Scrollable widget use the same one.
📍 المكان: during a scheduler callback

══╡ EXCEPTION CAUGHT BY SCHEDULER LIBRARY
╞═════════════════════════════════════════════════════════
The following assertion was thrown during a scheduler callback:
The Scrollbar's ScrollController has no ScrollPosition attached.
A Scrollbar cannot be painted without a ScrollPosition.
The Scrollbar attempted to use the provided ScrollController. This ScrollController should
be
associated with the ScrollView that the Scrollbar is being applied to.
When providing your own ScrollController, ensure both the Scrollbar and the Scrollable    
widget use
the same one.

When the exception was thrown, this was the stack:
#0      RawScrollbarState._debugCheckHasValidScrollPosition.<anonymous closure>
(package:flutter/src/widgets/scrollbar.dart:1470:9)
#1      RawScrollbarState._debugCheckHasValidScrollPosition
(package:flutter/src/widgets/scrollbar.dart:1496:6)
#2      RawScrollbarState._debugScheduleCheckHasValidScrollPosition.<anonymous closure>   
(package:flutter/src/widgets/scrollbar.dart:1427:14)
#3      SchedulerBinding._invokeFrameCallback
(package:flutter/src/scheduler/binding.dart:1438:15)
#4      SchedulerBinding.handleDrawFrame
(package:flutter/src/scheduler/binding.dart:1365:11)
#5      SchedulerBinding._handleDrawFrame
(package:flutter/src/scheduler/binding.dart:1204:5)
#6      _invoke (dart:ui/hooks.dart:331:13)
#7      PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:444:5)
#8      _drawFrame (dart:ui/hooks.dart:303:31)
══════════════════════════════════════════════════════════════════════════════════════════
══════════

NaN
NaN
❌ خطأ Flutter: A RenderFlex overflowed by 35 pixels on the bottom.
📍 المكان: during layout
Another exception was thrown: A RenderFlex overflowed by 35 pixels on the bottom.
🔍 فحص الاتصال...
NaN
NaN
❌ خطأ Flutter: The Scrollbar's ScrollController has no ScrollPosition attached.
A Scrollbar cannot be painted without a ScrollPosition.
The Scrollbar attempted to use the provided ScrollController. This ScrollController should be associated with the ScrollView that the Scrollbar is being applied to.
When providing your own ScrollController, ensure both the Scrollbar and the Scrollable widget use the same one.
📍 المكان: during a scheduler callback
Another exception was thrown: The Scrollbar's ScrollController has no ScrollPosition      
attached.
NaN
NaN
NaN
NaN
NaN
NaN
🔍 فحص الاتصال...
🔍 فحص الاتصال...
🔍 فحص الاتصال...
🧹 تنظيف الذاكرة...
🔍 فحص الاتصال...
🔍 فحص الاتصال...
🔍 فحص الاتصال...

🔍 فحص الاتصال...

ℹ️ PayPal معطل للسوق المصري
NaN
NaN
NaN
NaN
Performing hot reload...                                                
Reloaded 157 of 2872 libraries in 7,557ms (compile: 4618 ms, reload: 909 ms, reassemble:
1676 ms).

Performing hot restart...                                               
Restarted application in 5,269ms.
🚀 بدء تحسين الأداء للتطبيق على Windows...
✅ تم تحسين إعدادات النظام
🧹 تنظيف الذاكرة...
✅ تم تحسين إعدادات الذاكرة
🌐 تحسين إعدادات الشبكة...
✅ تم تحسين إعدادات الشبكة
✅ تم تحسين الأداء بنجاح
🔧 تهيئة خدمة الاستقرار...
✅ تم تهيئة خدمة الاستقرار بنجاح
ℹ️ PayPal معطل للسوق المصري
🔍 بدء تشخيص مشاكل تسجيل الدخول...
📧 الإيميل: <EMAIL>
🔒 كلمة المرور: ******

🔐 فحص Firebase Authentication...
✅ تم تسجيل الدخول بنجاح في Firebase Auth
📧 الإيميل: <EMAIL>
🆔 UID: qLV4Q2IUOIdBs2ZgpSIkPJvJeWE2
✅ تم التحقق: false
🔑 تم الحصول على ID Token بنجاح

🗄️ فحص Firebase Database...
✅ تم كتابة البيانات بنجاح
✅ تم قراءة البيانات بنجاح
📊 البيانات: {"message":"اختبار الاتصال","platform":"Windows","timestamp":1752689240860}  
✅ تم حذف البيانات التجريبية

👥 فحص بيانات المستخدمين الفرعيين...
ℹ️ لا توجد بيانات مستخدمين فرعيين
💡 هذا طبيعي إذا كان المستخدم رئيسي
✅ انتهاء التشخيص
🔐 تم تسجيل الدخول بنجاح في Firebase Auth
👤 المستخدم: <EMAIL>
🔍 بدء التحقق من المستخدمين الفرعيين للإيميل: <EMAIL>
📊 تم الحصول على بيانات User Role من Firebase
❌ لا توجد بيانات في Admin Panel/User Role
🔄 انتهاء التحقق - النتيجة: false (لا توجد بيانات)
🔍 نتيجة فحص المستخدم الفرعي: false
👤 مستخدم رئيسي - فحص إعداد الملف الشخصي
🔍 فحص إعداد الملف الشخصي...
🆔 معرف المستخدم: qLV4Q2IUOIdBs2ZgpSIkPJvJeWE2
📊 نتيجة الاستعلام: exists=true, value={businessCategory: Paints, companyName: Tayeb, countryName: Qina, currency: $, currentLocale: ar, dueInvoiceCounter: 1, gst: 0, language: العربية, phoneNumber: ***********, pictureUrl: https://firebasestorage.googleapis.com/v0/b/maanpos.appspot.com/o/Profile%20Picture%2Fblank-profile-picture-973460_1280.webp?alt=media&token=3578c1e0-7278-4c03-8b56-dd007a9befd3, purchaseInvoiceCounter: 1, remainingShopBalance: 0, saleInvoiceCounter: 1, shopOpeningBalance: 0}
✅ تم العثور على معلومات شخصية - الملف الشخصي معد
🔍 حالة إعداد الملف الشخصي: true
✅ الملف الشخصي معد - الانتقال للشاشة الرئيسية
NaN
NaN
unhandled element <filter/>; Picture key: Svg loader
NaN
NaN
NaN
NaN
❌ خطأ Flutter: The Scrollbar's ScrollController has no ScrollPosition attached.
A Scrollbar cannot be painted without a ScrollPosition.
The Scrollbar attempted to use the provided ScrollController. This ScrollController should be associated with the ScrollView that the Scrollbar is being applied to.
When providing your own ScrollController, ensure both the Scrollbar and the Scrollable widget use the same one.
📍 المكان: during a scheduler callback

══╡ EXCEPTION CAUGHT BY SCHEDULER LIBRARY
╞═════════════════════════════════════════════════════════
The following assertion was thrown during a scheduler callback:
The Scrollbar's ScrollController has no ScrollPosition attached.
A Scrollbar cannot be painted without a ScrollPosition.
The Scrollbar attempted to use the provided ScrollController. This ScrollController should
be
associated with the ScrollView that the Scrollbar is being applied to.
When providing your own ScrollController, ensure both the Scrollbar and the Scrollable    
widget use
the same one.

When the exception was thrown, this was the stack:
#0      RawScrollbarState._debugCheckHasValidScrollPosition.<anonymous closure>
(package:flutter/src/widgets/scrollbar.dart:1470:9)
#1      RawScrollbarState._debugCheckHasValidScrollPosition
(package:flutter/src/widgets/scrollbar.dart:1496:6)
#2      RawScrollbarState._debugScheduleCheckHasValidScrollPosition.<anonymous closure>   
(package:flutter/src/widgets/scrollbar.dart:1427:14)
#3      SchedulerBinding._invokeFrameCallback
(package:flutter/src/scheduler/binding.dart:1438:15)
#4      SchedulerBinding.handleDrawFrame
(package:flutter/src/scheduler/binding.dart:1365:11)
#5      SchedulerBinding._handleDrawFrame
(package:flutter/src/scheduler/binding.dart:1204:5)
#6      _invoke (dart:ui/hooks.dart:331:13)
#7      PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:444:5)
#8      _drawFrame (dart:ui/hooks.dart:303:31)
══════════════════════════════════════════════════════════════════════════════════════════
══════════

🔍 فحص الاتصال...
🔍 فحص الاتصال...
🔍 فحص الاتصال...

Performing hot restart...                                               
Restarted application in 3,078ms.
🚀 بدء تحسين الأداء للتطبيق على Windows...
✅ تم تحسين إعدادات النظام
🧹 تنظيف الذاكرة...
✅ تم تحسين إعدادات الذاكرة
🌐 تحسين إعدادات الشبكة...
✅ تم تحسين إعدادات الشبكة
✅ تم تحسين الأداء بنجاح
🔧 تهيئة خدمة الاستقرار...
✅ تم تهيئة خدمة الاستقرار بنجاح
ℹ️ PayPal معطل للسوق المصري
🔍 بدء تشخيص مشاكل تسجيل الدخول...
📧 الإيميل: <EMAIL>
🔒 كلمة المرور: ******

🔐 فحص Firebase Authentication...
✅ تم تسجيل الدخول بنجاح في Firebase Auth
📧 الإيميل: <EMAIL>
🆔 UID: qLV4Q2IUOIdBs2ZgpSIkPJvJeWE2
✅ تم التحقق: false
🔑 تم الحصول على ID Token بنجاح

🗄️ فحص Firebase Database...
✅ تم كتابة البيانات بنجاح
✅ تم قراءة البيانات بنجاح
📊 البيانات: {"message":"اختبار الاتصال","platform":"Windows","timestamp":1752689479775}  
✅ تم حذف البيانات التجريبية

👥 فحص بيانات المستخدمين الفرعيين...
ℹ️ لا توجد بيانات مستخدمين فرعيين
💡 هذا طبيعي إذا كان المستخدم رئيسي
✅ انتهاء التشخيص
🔐 تم تسجيل الدخول بنجاح في Firebase Auth
👤 المستخدم: <EMAIL>
🔍 بدء التحقق من المستخدمين الفرعيين للإيميل: <EMAIL>
📊 تم الحصول على بيانات User Role من Firebase
❌ لا توجد بيانات في Admin Panel/User Role
🔄 انتهاء التحقق - النتيجة: false (لا توجد بيانات)
🔍 نتيجة فحص المستخدم الفرعي: false
👤 مستخدم رئيسي - فحص إعداد الملف الشخصي
🔍 فحص إعداد الملف الشخصي...
🆔 معرف المستخدم: qLV4Q2IUOIdBs2ZgpSIkPJvJeWE2
📊 نتيجة الاستعلام: exists=true, value={businessCategory: Paints, companyName: Tayeb, countryName: Qina, currency: $, currentLocale: ar, dueInvoiceCounter: 1, gst: 0, language: العربية, phoneNumber: ***********, pictureUrl: https://firebasestorage.googleapis.com/v0/b/maanpos.appspot.com/o/Profile%20Picture%2Fblank-profile-picture-973460_1280.webp?alt=media&token=3578c1e0-7278-4c03-8b56-dd007a9befd3, purchaseInvoiceCounter: 1, remainingShopBalance: 0, saleInvoiceCounter: 1, shopOpeningBalance: 0}
✅ تم العثور على معلومات شخصية - الملف الشخصي معد
🔍 حالة إعداد الملف الشخصي: true
✅ الملف الشخصي معد - الانتقال للشاشة الرئيسية
NaN
NaN
unhandled element <filter/>; Picture key: Svg loader
NaN
NaN
❌ خطأ Flutter: The Scrollbar's ScrollController has no ScrollPosition attached.
A Scrollbar cannot be painted without a ScrollPosition.
The Scrollbar attempted to use the provided ScrollController. This ScrollController should be associated with the ScrollView that the Scrollbar is being applied to.
When providing your own ScrollController, ensure both the Scrollbar and the Scrollable widget use the same one.
📍 المكان: during a scheduler callback

══╡ EXCEPTION CAUGHT BY SCHEDULER LIBRARY
╞═════════════════════════════════════════════════════════
The following assertion was thrown during a scheduler callback:
The Scrollbar's ScrollController has no ScrollPosition attached.
A Scrollbar cannot be painted without a ScrollPosition.
The Scrollbar attempted to use the provided ScrollController. This ScrollController should
be
associated with the ScrollView that the Scrollbar is being applied to.
When providing your own ScrollController, ensure both the Scrollbar and the Scrollable    
widget use
the same one.

When the exception was thrown, this was the stack:
#0      RawScrollbarState._debugCheckHasValidScrollPosition.<anonymous closure>
(package:flutter/src/widgets/scrollbar.dart:1470:9)
#1      RawScrollbarState._debugCheckHasValidScrollPosition
(package:flutter/src/widgets/scrollbar.dart:1496:6)
#2      RawScrollbarState._debugScheduleCheckHasValidScrollPosition.<anonymous closure>   
(package:flutter/src/widgets/scrollbar.dart:1427:14)
#3      SchedulerBinding._invokeFrameCallback
(package:flutter/src/scheduler/binding.dart:1438:15)
#4      SchedulerBinding.handleDrawFrame
(package:flutter/src/scheduler/binding.dart:1365:11)
#5      SchedulerBinding._handleDrawFrame
(package:flutter/src/scheduler/binding.dart:1204:5)
#6      _invoke (dart:ui/hooks.dart:331:13)
#7      PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:444:5)
#8      _drawFrame (dart:ui/hooks.dart:303:31)
══════════════════════════════════════════════════════════════════════════════════════════
══════════

🔍 فحص الاتصال...
🔍 فحص الاتصال...
🔍 فحص الاتصال...
[]
🔍 فحص الاتصال...
🧹 تنظيف الذاكرة...
🔍 فحص الاتصال...
🔍 فحص الاتصال...
🔍 فحص الاتصال...
🔍 فحص الاتصال...



Performing hot restart...                                               
Restarted application in 5,428ms.
🚀 بدء تحسين الأداء للتطبيق على Windows...
✅ تم تحسين إعدادات النظام
🧹 تنظيف الذاكرة...
✅ تم تحسين إعدادات الذاكرة
🌐 تحسين إعدادات الشبكة...
✅ تم تحسين إعدادات الشبكة
✅ تم تحسين الأداء بنجاح
🔧 تهيئة خدمة الاستقرار...
✅ تم تهيئة خدمة الاستقرار بنجاح
ℹ️ PayPal معطل للسوق المصري

🔍 بدء تشخيص مشاكل تسجيل الدخول...
📧 الإيميل: <EMAIL>
🔒 كلمة المرور: ******

🔐 فحص Firebase Authentication...
✅ تم تسجيل الدخول بنجاح في Firebase Auth
📧 الإيميل: <EMAIL>
🆔 UID: qLV4Q2IUOIdBs2ZgpSIkPJvJeWE2
✅ تم التحقق: false
🔑 تم الحصول على ID Token بنجاح

🗄️ فحص Firebase Database...
✅ تم كتابة البيانات بنجاح
✅ تم قراءة البيانات بنجاح
📊 البيانات: {"message":"اختبار الاتصال","platform":"Windows","timestamp":1752689984290}  
✅ تم حذف البيانات التجريبية

👥 فحص بيانات المستخدمين الفرعيين...
ℹ️ لا توجد بيانات مستخدمين فرعيين
💡 هذا طبيعي إذا كان المستخدم رئيسي
✅ انتهاء التشخيص
🔐 تم تسجيل الدخول بنجاح في Firebase Auth
👤 المستخدم: <EMAIL>
🔍 بدء التحقق من المستخدمين الفرعيين للإيميل: <EMAIL>
📊 تم الحصول على بيانات User Role من Firebase
❌ لا توجد بيانات في Admin Panel/User Role
🔄 انتهاء التحقق - النتيجة: false (لا توجد بيانات)
🔍 نتيجة فحص المستخدم الفرعي: false
👤 مستخدم رئيسي - فحص إعداد الملف الشخصي
🔍 فحص إعداد الملف الشخصي...
🆔 معرف المستخدم: qLV4Q2IUOIdBs2ZgpSIkPJvJeWE2

══╡ EXCEPTION CAUGHT BY SCHEDULER LIBRARY
╞═════════════════════════════════════════════════════════
The following assertion was thrown during a scheduler callback:
The Scrollbar's ScrollController has no ScrollPosition attached.
A Scrollbar cannot be painted without a ScrollPosition.
The Scrollbar attempted to use the provided ScrollController. This ScrollController should
be
associated with the ScrollView that the Scrollbar is being applied to.
When providing your own ScrollController, ensure both the Scrollbar and the Scrollable    
widget use
the same one.

When the exception was thrown, this was the stack:
#0      RawScrollbarState._debugCheckHasValidScrollPosition.<anonymous closure>
(package:flutter/src/widgets/scrollbar.dart:1470:9)
#1      RawScrollbarState._debugCheckHasValidScrollPosition
(package:flutter/src/widgets/scrollbar.dart:1496:6)
#2      RawScrollbarState._debugScheduleCheckHasValidScrollPosition.<anonymous closure>   
(package:flutter/src/widgets/scrollbar.dart:1427:14)
#3      SchedulerBinding._invokeFrameCallback
(package:flutter/src/scheduler/binding.dart:1438:15)
#4      SchedulerBinding.handleDrawFrame
(package:flutter/src/scheduler/binding.dart:1365:11)
#5      SchedulerBinding._handleDrawFrame
(package:flutter/src/scheduler/binding.dart:1204:5)
#6      _invoke (dart:ui/hooks.dart:331:13)
#7      PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:444:5)
#8      _drawFrame (dart:ui/hooks.dart:303:31)
══════════════════════════════════════════════════════════════════════════════════════════
══════════

📊 نتيجة الاستعلام: exists=true, value={businessCategory: Paints, companyName: Tayeb, countryName: Qina, currency: $, currentLocale: ar, dueInvoiceCounter: 1, gst: 0, language: العربية, phoneNumber: ***********, pictureUrl: https://firebasestorage.googleapis.com/v0/b/maanpos.appspot.com/o/Profile%20Picture%2Fblank-profile-picture-973460_1280.webp?alt=media&token=3578c1e0-7278-4c03-8b56-dd007a9befd3, purchaseInvoiceCounter: 1, remainingShopBalance: 0, saleInvoiceCounter: 1, shopOpeningBalance: 0}
✅ تم العثور على معلومات شخصية - الملف الشخصي معد
🔍 حالة إعداد الملف الشخصي: true
✅ الملف الشخصي معد - الانتقال للشاشة الرئيسية
NaN
NaN
unhandled element <filter/>; Picture key: Svg loader
Another exception was thrown: A RenderFlex overflowed by 35 pixels on the bottom.
NaN
NaN
❌ خطأ Flutter: The Scrollbar's ScrollController has no ScrollPosition attached.
A Scrollbar cannot be painted without a ScrollPosition.
The Scrollbar attempted to use the provided ScrollController. This ScrollController should be associated with the ScrollView that the Scrollbar is being applied to.
When providing your own ScrollController, ensure both the Scrollbar and the Scrollable widget use the same one.
📍 المكان: during a scheduler callback
❌ خطأ Flutter: A RenderFlex overflowed by 35 pixels on the bottom.
📍 المكان: during layout
🔍 فحص الاتصال...
🔍 فحص الاتصال...
🔍 فحص الاتصال...
🔍 فحص الاتصال...
🧹 تنظيف الذاكرة...
🔍 فحص الاتصال...
🔍 فحص الاتصال...
🔍 فحص الاتصال...
🔍 فحص الاتصال...
🔍 فحص الاتصال...
🧹 تنظيف الذاكرة...
🔍 فحص الاتصال...
🔍 فحص الاتصال...
🔍 فحص الاتصال...
🔍 فحص الاتصال...
🔍 فحص الاتصال...
🧹 تنظيف الذاكرة...
🔍 فحص الاتصال...
🔍 فحص الاتصال...
🔍 فحص الاتصال...
🔍 فحص الاتصال...
🔍 فحص الاتصال...
🧹 تنظيف الذاكرة...
🔍 فحص الاتصال...

🔍 فحص الاتصال...
NaN
NaN
Performing hot restart...                                               
Restarted application in 37,056ms.
🚀 بدء تحسين الأداء للتطبيق على Windows...
✅ تم تحسين إعدادات النظام
🧹 تنظيف الذاكرة...
✅ تم تحسين إعدادات الذاكرة
🌐 تحسين إعدادات الشبكة...
✅ تم تحسين إعدادات الشبكة
✅ تم تحسين الأداء بنجاح
🔧 تهيئة خدمة الاستقرار...
✅ تم تهيئة خدمة الاستقرار بنجاح
ℹ️ PayPal معطل للسوق المصري
🔍 بدء تشخيص مشاكل تسجيل الدخول...
📧 الإيميل: <EMAIL>
🔒 كلمة المرور: ******

🔐 فحص Firebase Authentication...
✅ تم تسجيل الدخول بنجاح في Firebase Auth
📧 الإيميل: <EMAIL>
🆔 UID: qLV4Q2IUOIdBs2ZgpSIkPJvJeWE2
✅ تم التحقق: false
🔑 تم الحصول على ID Token بنجاح

🗄️ فحص Firebase Database...
✅ تم كتابة البيانات بنجاح
✅ تم قراءة البيانات بنجاح
📊 البيانات: {"message":"اختبار الاتصال","platform":"Windows","timestamp":1752691274066}  
✅ تم حذف البيانات التجريبية

👥 فحص بيانات المستخدمين الفرعيين...
ℹ️ لا توجد بيانات مستخدمين فرعيين
💡 هذا طبيعي إذا كان المستخدم رئيسي
✅ انتهاء التشخيص
🔐 تم تسجيل الدخول بنجاح في Firebase Auth
👤 المستخدم: <EMAIL>
🔍 بدء التحقق من المستخدمين الفرعيين للإيميل: <EMAIL>
📊 تم الحصول على بيانات User Role من Firebase
❌ لا توجد بيانات في Admin Panel/User Role
🔄 انتهاء التحقق - النتيجة: false (لا توجد بيانات)
🔍 نتيجة فحص المستخدم الفرعي: false
👤 مستخدم رئيسي - فحص إعداد الملف الشخصي
🔍 فحص إعداد الملف الشخصي...
🆔 معرف المستخدم: qLV4Q2IUOIdBs2ZgpSIkPJvJeWE2
📊 نتيجة الاستعلام: exists=true, value={businessCategory: Paints, companyName: Tayeb, countryName: Qina, currency: $, currentLocale: ar, dueInvoiceCounter: 1, gst: 0, language: العربية, phoneNumber: ***********, pictureUrl: https://firebasestorage.googleapis.com/v0/b/maanpos.appspot.com/o/Profile%20Picture%2Fblank-profile-picture-973460_1280.webp?alt=media&token=3578c1e0-7278-4c03-8b56-dd007a9befd3, purchaseInvoiceCounter: 1, remainingShopBalance: 0, saleInvoiceCounter: 1, shopOpeningBalance: 0}
✅ تم العثور على معلومات شخصية - الملف الشخصي معد
🔍 حالة إعداد الملف الشخصي: true
✅ الملف الشخصي معد - الانتقال للشاشة الرئيسية
NaN
NaN
unhandled element <filter/>; Picture key: Svg loader
NaN
NaN
NaN
NaN
❌ خطأ Flutter: The Scrollbar's ScrollController has no ScrollPosition attached.
A Scrollbar cannot be painted without a ScrollPosition.
The Scrollbar attempted to use the provided ScrollController. This ScrollController should be associated with the ScrollView that the Scrollbar is being applied to.
When providing your own ScrollController, ensure both the Scrollbar and the Scrollable widget use the same one.
📍 المكان: during a scheduler callback

══╡ EXCEPTION CAUGHT BY SCHEDULER LIBRARY
╞═════════════════════════════════════════════════════════
The following assertion was thrown during a scheduler callback:
The Scrollbar's ScrollController has no ScrollPosition attached.
A Scrollbar cannot be painted without a ScrollPosition.
The Scrollbar attempted to use the provided ScrollController. This ScrollController should
be
associated with the ScrollView that the Scrollbar is being applied to.
When providing your own ScrollController, ensure both the Scrollbar and the Scrollable    
widget use
the same one.

When the exception was thrown, this was the stack:
#0      RawScrollbarState._debugCheckHasValidScrollPosition.<anonymous closure>
(package:flutter/src/widgets/scrollbar.dart:1470:9)
#1      RawScrollbarState._debugCheckHasValidScrollPosition
(package:flutter/src/widgets/scrollbar.dart:1496:6)
#2      RawScrollbarState._debugScheduleCheckHasValidScrollPosition.<anonymous closure>   
(package:flutter/src/widgets/scrollbar.dart:1427:14)
#3      SchedulerBinding._invokeFrameCallback
(package:flutter/src/scheduler/binding.dart:1438:15)
#4      SchedulerBinding.handleDrawFrame
(package:flutter/src/scheduler/binding.dart:1365:11)
#5      SchedulerBinding._handleDrawFrame
(package:flutter/src/scheduler/binding.dart:1204:5)
#6      _invoke (dart:ui/hooks.dart:331:13)
#7      PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:444:5)
#8      _drawFrame (dart:ui/hooks.dart:303:31)
══════════════════════════════════════════════════════════════════════════════════════════
══════════

❌ خطأ Flutter: A RenderFlex overflowed by 35 pixels on the bottom.
📍 المكان: during layout
Another exception was thrown: A RenderFlex overflowed by 35 pixels on the bottom.
❌ خطأ Flutter: 'package:flutter/src/material/dropdown.dart': Failed assertion: line 1003 pos 10: 'items == null ||

             items.isEmpty ||

             value == null ||

             items.where((DropdownMenuItem<T> item) {

                   return item.value == value;

                 }).length ==

                 1': There should be exactly one item with [DropdownButton]'s value: Guest.
Either zero or 2 or more [DropdownMenuItem]s were detected with the same value
📍 المكان: building Consumer(dirty, dependencies: [MediaQuery, UncontrolledProviderScope, _LocalizationsScope-[GlobalKey#b6c4d]], state: _ConsumerState#9261c)
Another exception was thrown: There should be exactly one item with [DropdownButton]'s    
value: Guest.
🔍 فحص الاتصال...
🔍 فحص الاتصال...
🔍 فحص الاتصال...
🔍 فحص الاتصال...
🧹 تنظيف الذاكرة...
🔍 فحص الاتصال...
🔍 فحص الاتصال...

NaN
NaN
❌ خطأ Flutter: 'package:flutter/src/material/dropdown.dart': Failed assertion: line 1003 pos 10: 'items == null ||

             items.isEmpty ||

             value == null ||

             items.where((DropdownMenuItem<T> item) {

                   return item.value == value;

                 }).length ==

                 1': There should be exactly one item with [DropdownButton]'s value: Guest.
Either zero or 2 or more [DropdownMenuItem]s were detected with the same value
📍 المكان: building Consumer(dirty, dependencies: [MediaQuery, UncontrolledProviderScope, _LocalizationsScope-[GlobalKey#b6c4d]], state: _ConsumerState#9261c)
Another exception was thrown: There should be exactly one item with [DropdownButton]'s
value: Guest.
Performing hot restart...                                               
Restarted application in 6,212ms.
🚀 بدء تحسين الأداء للتطبيق على Windows...
✅ تم تحسين إعدادات النظام
🧹 تنظيف الذاكرة...
✅ تم تحسين إعدادات الذاكرة
🌐 تحسين إعدادات الشبكة...
✅ تم تحسين إعدادات الشبكة
✅ تم تحسين الأداء بنجاح
🔧 تهيئة خدمة الاستقرار...
✅ تم تهيئة خدمة الاستقرار بنجاح
ℹ️ PayPal معطل للسوق المصري
🔍 بدء تشخيص مشاكل تسجيل الدخول...
📧 الإيميل: <EMAIL>
🔒 كلمة المرور: ******

🔐 فحص Firebase Authentication...
✅ تم تسجيل الدخول بنجاح في Firebase Auth
📧 الإيميل: <EMAIL>
🆔 UID: qLV4Q2IUOIdBs2ZgpSIkPJvJeWE2
✅ تم التحقق: false
🔑 تم الحصول على ID Token بنجاح

🗄️ فحص Firebase Database...
✅ تم كتابة البيانات بنجاح
✅ تم قراءة البيانات بنجاح
📊 البيانات: {"message":"اختبار الاتصال","platform":"Windows","timestamp":1752691679093}  
✅ تم حذف البيانات التجريبية

👥 فحص بيانات المستخدمين الفرعيين...
ℹ️ لا توجد بيانات مستخدمين فرعيين
💡 هذا طبيعي إذا كان المستخدم رئيسي
✅ انتهاء التشخيص
🔐 تم تسجيل الدخول بنجاح في Firebase Auth
👤 المستخدم: <EMAIL>
🔍 بدء التحقق من المستخدمين الفرعيين للإيميل: <EMAIL>
📊 تم الحصول على بيانات User Role من Firebase
❌ لا توجد بيانات في Admin Panel/User Role
🔄 انتهاء التحقق - النتيجة: false (لا توجد بيانات)
🔍 نتيجة فحص المستخدم الفرعي: false
👤 مستخدم رئيسي - فحص إعداد الملف الشخصي
🔍 فحص إعداد الملف الشخصي...
🆔 معرف المستخدم: qLV4Q2IUOIdBs2ZgpSIkPJvJeWE2
📊 نتيجة الاستعلام: exists=true, value={businessCategory: Paints, companyName: Tayeb, countryName: Qina, currency: $, currentLocale: ar, dueInvoiceCounter: 1, gst: 0, language: العربية, phoneNumber: ***********, pictureUrl: https://firebasestorage.googleapis.com/v0/b/maanpos.appspot.com/o/Profile%20Picture%2Fblank-profile-picture-973460_1280.webp?alt=media&token=3578c1e0-7278-4c03-8b56-dd007a9befd3, purchaseInvoiceCounter: 1, remainingShopBalance: 0, saleInvoiceCounter: 1, shopOpeningBalance: 0}
✅ تم العثور على معلومات شخصية - الملف الشخصي معد
🔍 حالة إعداد الملف الشخصي: true
✅ الملف الشخصي معد - الانتقال للشاشة الرئيسية
NaN
NaN
unhandled element <filter/>; Picture key: Svg loader
NaN
NaN
❌ خطأ Flutter: The Scrollbar's ScrollController has no ScrollPosition attached.
A Scrollbar cannot be painted without a ScrollPosition.
The Scrollbar attempted to use the provided ScrollController. This ScrollController should be associated with the ScrollView that the Scrollbar is being applied to.
When providing your own ScrollController, ensure both the Scrollbar and the Scrollable widget use the same one.
📍 المكان: during a scheduler callback

══╡ EXCEPTION CAUGHT BY SCHEDULER LIBRARY
╞═════════════════════════════════════════════════════════
The following assertion was thrown during a scheduler callback:
The Scrollbar's ScrollController has no ScrollPosition attached.
A Scrollbar cannot be painted without a ScrollPosition.
The Scrollbar attempted to use the provided ScrollController. This ScrollController should
be
associated with the ScrollView that the Scrollbar is being applied to.
When providing your own ScrollController, ensure both the Scrollbar and the Scrollable    
widget use
the same one.

When the exception was thrown, this was the stack:
#0      RawScrollbarState._debugCheckHasValidScrollPosition.<anonymous closure>
(package:flutter/src/widgets/scrollbar.dart:1470:9)
#1      RawScrollbarState._debugCheckHasValidScrollPosition
(package:flutter/src/widgets/scrollbar.dart:1496:6)
#2      RawScrollbarState._debugScheduleCheckHasValidScrollPosition.<anonymous closure>   
(package:flutter/src/widgets/scrollbar.dart:1427:14)
#3      SchedulerBinding._invokeFrameCallback
(package:flutter/src/scheduler/binding.dart:1438:15)
#4      SchedulerBinding.handleDrawFrame
(package:flutter/src/scheduler/binding.dart:1365:11)
#5      SchedulerBinding._handleDrawFrame
(package:flutter/src/scheduler/binding.dart:1204:5)
#6      _invoke (dart:ui/hooks.dart:331:13)
#7      PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:444:5)
#8      _drawFrame (dart:ui/hooks.dart:303:31)
══════════════════════════════════════════════════════════════════════════════════════════
══════════

❌ خطأ Flutter: A RenderFlex overflowed by 35 pixels on the bottom.
📍 المكان: during layout
Another exception was thrown: A RenderFlex overflowed by 35 pixels on the bottom.
❌ خطأ Flutter: 'package:flutter/src/material/dropdown.dart': Failed assertion: line 1003 pos 10: 'items == null ||

             items.isEmpty ||

             value == null ||

             items.where((DropdownMenuItem<T> item) {

                   return item.value == value;

                 }).length ==

                 1': There should be exactly one item with [DropdownButton]'s value: Guest.
Either zero or 2 or more [DropdownMenuItem]s were detected with the same value
📍 المكان: building Consumer(dirty, dependencies: [MediaQuery, UncontrolledProviderScope, _LocalizationsScope-[GlobalKey#5ab3b]], state: _ConsumerState#333ce)
Another exception was thrown: There should be exactly one item with [DropdownButton]'s    
value: Guest.

Performing hot restart...                                               
Restarted application in 7,854ms.
🚀 بدء تحسين الأداء للتطبيق على Windows...
✅ تم تحسين إعدادات النظام
🧹 تنظيف الذاكرة...
✅ تم تحسين إعدادات الذاكرة
🌐 تحسين إعدادات الشبكة...
✅ تم تحسين إعدادات الشبكة
✅ تم تحسين الأداء بنجاح
🔧 تهيئة خدمة الاستقرار...
✅ تم تهيئة خدمة الاستقرار بنجاح
ℹ️ PayPal معطل للسوق المصري

🔍 بدء تشخيص مشاكل تسجيل الدخول...
📧 الإيميل: <EMAIL>
🔒 كلمة المرور: ******

🔐 فحص Firebase Authentication...
✅ تم تسجيل الدخول بنجاح في Firebase Auth
📧 الإيميل: <EMAIL>
🆔 UID: qLV4Q2IUOIdBs2ZgpSIkPJvJeWE2
✅ تم التحقق: false
🔑 تم الحصول على ID Token بنجاح

🗄️ فحص Firebase Database...
✅ تم كتابة البيانات بنجاح
✅ تم قراءة البيانات بنجاح
📊 البيانات: {"message":"اختبار الاتصال","platform":"Windows","timestamp":1752691730436}  
✅ تم حذف البيانات التجريبية

👥 فحص بيانات المستخدمين الفرعيين...
ℹ️ لا توجد بيانات مستخدمين فرعيين
💡 هذا طبيعي إذا كان المستخدم رئيسي
✅ انتهاء التشخيص
🔐 تم تسجيل الدخول بنجاح في Firebase Auth
👤 المستخدم: <EMAIL>
🔍 بدء التحقق من المستخدمين الفرعيين للإيميل: <EMAIL>
📊 تم الحصول على بيانات User Role من Firebase
❌ لا توجد بيانات في Admin Panel/User Role
🔄 انتهاء التحقق - النتيجة: false (لا توجد بيانات)
🔍 نتيجة فحص المستخدم الفرعي: false
👤 مستخدم رئيسي - فحص إعداد الملف الشخصي
🔍 فحص إعداد الملف الشخصي...
🆔 معرف المستخدم: qLV4Q2IUOIdBs2ZgpSIkPJvJeWE2
📊 نتيجة الاستعلام: exists=true, value={businessCategory: Paints, companyName: Tayeb, countryName: Qina, currency: $, currentLocale: ar, dueInvoiceCounter: 1, gst: 0, language: العربية, phoneNumber: ***********, pictureUrl: https://firebasestorage.googleapis.com/v0/b/maanpos.appspot.com/o/Profile%20Picture%2Fblank-profile-picture-973460_1280.webp?alt=media&token=3578c1e0-7278-4c03-8b56-dd007a9befd3, purchaseInvoiceCounter: 1, remainingShopBalance: 0, saleInvoiceCounter: 1, shopOpeningBalance: 0}
✅ تم العثور على معلومات شخصية - الملف الشخصي معد
🔍 حالة إعداد الملف الشخصي: true
✅ الملف الشخصي معد - الانتقال للشاشة الرئيسية
NaN
NaN
unhandled element <filter/>; Picture key: Svg loader
NaN
NaN
❌ خطأ Flutter: The Scrollbar's ScrollController has no ScrollPosition attached.
A Scrollbar cannot be painted without a ScrollPosition.
The Scrollbar attempted to use the provided ScrollController. This ScrollController should be associated with the ScrollView that the Scrollbar is being applied to.
When providing your own ScrollController, ensure both the Scrollbar and the Scrollable widget use the same one.
📍 المكان: during a scheduler callback

══╡ EXCEPTION CAUGHT BY SCHEDULER LIBRARY
╞═════════════════════════════════════════════════════════
The following assertion was thrown during a scheduler callback:
The Scrollbar's ScrollController has no ScrollPosition attached.
A Scrollbar cannot be painted without a ScrollPosition.
The Scrollbar attempted to use the provided ScrollController. This ScrollController should
be
associated with the ScrollView that the Scrollbar is being applied to.
When providing your own ScrollController, ensure both the Scrollbar and the Scrollable    
widget use
the same one.

When the exception was thrown, this was the stack:
#0      RawScrollbarState._debugCheckHasValidScrollPosition.<anonymous closure>
(package:flutter/src/widgets/scrollbar.dart:1470:9)
#1      RawScrollbarState._debugCheckHasValidScrollPosition
(package:flutter/src/widgets/scrollbar.dart:1496:6)
#2      RawScrollbarState._debugScheduleCheckHasValidScrollPosition.<anonymous closure>   
(package:flutter/src/widgets/scrollbar.dart:1427:14)
#3      SchedulerBinding._invokeFrameCallback
(package:flutter/src/scheduler/binding.dart:1438:15)
#4      SchedulerBinding.handleDrawFrame
(package:flutter/src/scheduler/binding.dart:1365:11)
#5      SchedulerBinding._handleDrawFrame
(package:flutter/src/scheduler/binding.dart:1204:5)
#6      _invoke (dart:ui/hooks.dart:331:13)
#7      PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:444:5)
#8      _drawFrame (dart:ui/hooks.dart:303:31)
══════════════════════════════════════════════════════════════════════════════════════════
══════════

NaN
NaN

❌ خطأ Flutter: 'package:flutter/src/material/dropdown.dart': Failed assertion: line 1003 pos 10: 'items == null ||

             items.isEmpty ||

             value == null ||

             items.where((DropdownMenuItem<T> item) {

                   return item.value == value;

                 }).length ==

                 1': There should be exactly one item with [DropdownButton]'s value: Guest.
Either zero or 2 or more [DropdownMenuItem]s were detected with the same value
📍 المكان: building Consumer(dirty, dependencies: [MediaQuery, UncontrolledProviderScope, _LocalizationsScope-[GlobalKey#94e0e]], state: _ConsumerState#a1fdc)
Another exception was thrown: There should be exactly one item with [DropdownButton]'s
value: Guest.
❌ خطأ Flutter: 'package:flutter/src/material/dropdown.dart': Failed assertion: line 1003 pos 10: 'items == null ||

             items.isEmpty ||

             value == null ||

             items.where((DropdownMenuItem<T> item) {

                   return item.value == value;

                 }).length ==

                 1': There should be exactly one item with [DropdownButton]'s value: Guest.
Either zero or 2 or more [DropdownMenuItem]s were detected with the same value
📍 المكان: building Consumer(dirty, dependencies: [MediaQuery, UncontrolledProviderScope, _LocalizationsScope-[GlobalKey#94e0e]], state: _ConsumerState#a1fdc)
Another exception was thrown: There should be exactly one item with [DropdownButton]'s
value: Guest.
🔍 فحص الاتصال...
