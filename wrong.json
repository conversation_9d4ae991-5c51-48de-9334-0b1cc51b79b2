❌ خطأ Flutter: The Scrollbar's ScrollController has no ScrollPosition attached.
A Scrollbar cannot be painted without a ScrollPosition.
The Scrollbar attempted to use the provided ScrollController. This ScrollController should be associated with the ScrollView that the Scrollbar is being applied to.
When providing your own ScrollController, ensure both the Scrollbar and the Scrollable widget use the same one.
📍 المكان: during a scheduler callback

══╡ EXCEPTION CAUGHT BY SCHEDULER LIBRARY
╞═════════════════════════════════════════════════════════
The following assertion was thrown during a scheduler callback:
The Scrollbar's ScrollController has no ScrollPosition attached.
A Scrollbar cannot be painted without a ScrollPosition.
The Scrollbar attempted to use the provided ScrollController. This ScrollController should
be
associated with the ScrollView that the Scrollbar is being applied to.
When providing your own ScrollController, ensure both the Scrollbar and the Scrollable    
widget use
the same one.

When the exception was thrown, this was the stack:
#0      RawScrollbarState._debugCheckHasValidScrollPosition.<anonymous closure>
(package:flutter/src/widgets/scrollbar.dart:1470:9)
#1      RawScrollbarState._debugCheckHasValidScrollPosition
(package:flutter/src/widgets/scrollbar.dart:1496:6)
#2      RawScrollbarState._debugScheduleCheckHasValidScrollPosition.<anonymous closure>   
(package:flutter/src/widgets/scrollbar.dart:1427:14)
#3      SchedulerBinding._invokeFrameCallback
(package:flutter/src/scheduler/binding.dart:1438:15)
#4      SchedulerBinding.handleDrawFrame
(package:flutter/src/scheduler/binding.dart:1365:11)
#5      SchedulerBinding._handleDrawFrame
(package:flutter/src/scheduler/binding.dart:1204:5)
#6      _invoke (dart:ui/hooks.dart:331:13)
#7      PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:444:5)
#8      _drawFrame (dart:ui/hooks.dart:303:31)
══════════════════════════════════════════════════════════════════════════════════════════
══════════

❌ خطأ Flutter: A RenderFlex overflowed by 35 pixels on the bottom.
📍 المكان: during layout
Another exception was thrown: A RenderFlex overflowed by 35 pixels on the bottom.
NaN
NaN
❌ خطأ Flutter: The Scrollbar's ScrollController has no ScrollPosition attached.
A Scrollbar cannot be painted without a ScrollPosition.
The Scrollbar attempted to use the provided ScrollController. This ScrollController should be associated with the ScrollView that the Scrollbar is being applied to.
When providing your own ScrollController, ensure both the Scrollbar and the Scrollable widget use the same one.
📍 المكان: during a scheduler callback
Another exception was thrown: The Scrollbar's ScrollController has no ScrollPosition
attached.
NaN
NaN
Lost connection to device.